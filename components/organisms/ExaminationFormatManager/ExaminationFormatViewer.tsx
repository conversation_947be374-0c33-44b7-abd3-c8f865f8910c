'use client';

import React, { useState, useEffect } from 'react';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { Button } from '@/components/atoms/Button/Button';
import { getExaminationFormatAction } from '@/actions/examinationFormat.action';
import { Download, ExternalLink, Calendar, FileText, User, Loader2, RefreshCw, Replace, Upload } from 'lucide-react';
import { ExaminationFormatDialog } from './ExaminationFormatDialog';

interface ExaminationFormatViewerProps {
  schoolId: string;
  schoolName?: string;
  refreshTrigger?: number; // Increment this to trigger a refresh
  className?: string;
  metadata?: {
    uploadedAt?: string;
    fileSize?: number;
    uploader?: string;
  };
  onReplaceClick?: () => void;
}

export const ExaminationFormatViewer: React.FC<ExaminationFormatViewerProps> = ({
  schoolId,
  schoolName,
  refreshTrigger = 0,
  className,
  metadata,
  onReplaceClick,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formatData, setFormatData] = useState<{
    text?: string;
    url?: string;
    contentType?: string;
  } | null>(null);
  const [isPdfLoading, setIsPdfLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [validationStatus, setValidationStatus] = useState<'valid' | 'warning' | 'unknown'>('unknown');

  const fetchFormat = async () => {
    if (!schoolId) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await getExaminationFormatAction(schoolId);
      
      if (response.status === 'success') {
        setFormatData(response.data);
        // Simulate format validation check
        setValidationStatus(Math.random() > 0.3 ? 'valid' : 'warning');
      } else {
        setError(typeof response.message === 'string' ? response.message : 'Failed to load examination format');
        setFormatData(null);
        setValidationStatus('unknown');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      setFormatData(null);
      setValidationStatus('unknown');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchFormat();
  }, [schoolId, refreshTrigger]);

  const handlePdfLoad = () => {
    setIsPdfLoading(false);
  };

  const handlePdfError = () => {
    setIsPdfLoading(false);
    setError('Failed to load PDF preview');
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown';
    if (bytes < 1024) return `${bytes} bytes`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
  };

  // Determine if the URL is a data URL (base64 encoded)
  const isDataUrl = formatData?.url?.startsWith('data:');

  const openDialog = () => {
    setIsDialogOpen(true);
  };

  const closeDialog = () => {
    setIsDialogOpen(false);
  };

  const getValidationStatusIcon = () => {
    switch (validationStatus) {
      case 'valid':
        return <div className="text-green-500 bg-green-50 p-1 rounded-full">✓</div>;
      case 'warning':
        return <div className="text-amber-500 bg-amber-50 p-1 rounded-full">⚠</div>;
      default:
        return null;
    }
  };

  const getValidationStatusText = () => {
    switch (validationStatus) {
      case 'valid':
        return <span className="text-green-600 text-xs font-medium">Compatible format</span>;
      case 'warning':
        return <span className="text-amber-600 text-xs font-medium">Format needs review</span>;
      default:
        return null;
    }
  };

  return (
    <>
      <div className={`p-6 ${className || ''}`}>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">
            {schoolName ? `${schoolName} Format` : 'Examination Format'}
          </h3>
          
          <div className="flex space-x-2">
            {formatData?.url && (
              <Button
                variant="outline"
                iconProps={{ variant: "eye" }}
                onClick={openDialog}
                className="flex items-center !w-auto"
              >
                 View Format
              </Button>
            )}
          </div>
        </div>

        {/* Content Preview Card */}
        <div 
          className={`border rounded-lg overflow-hidden bg-white transition-all duration-300 ${formatData ? 'cursor-pointer hover:border-blue-300 hover:shadow-md' : ''}`}
          onClick={formatData ? openDialog : undefined}
          role={formatData ? "button" : undefined}
          aria-label={formatData ? "View examination format" : undefined}
          tabIndex={formatData ? 0 : undefined}
        >
          <div className="flex flex-col md:flex-row">
            {/* Preview Thumbnail */}
            <div className="w-full md:w-1/3 bg-gray-50 p-6 flex items-center justify-center border-b md:border-b-0 md:border-r border-gray-100">
              {isLoading ? (
                <div className="flex items-center justify-center p-8">
                  <Loader2 size={32} className="animate-spin text-blue-600" />
                </div>
              ) : error ? (
                <div className="flex items-center justify-center p-4">
                  <AlertMessage type="error" message={error} />
                </div>
              ) : !formatData ? (
                <div className="flex flex-col items-center justify-center p-8 text-center">
                  <FileText size={48} className="text-gray-300 mb-3" />
                  <p className="text-gray-500 mb-2">No format available</p>
                  <p className="text-sm text-gray-400">Upload to see preview</p>
                </div>
              ) : (
                <div className="relative w-full h-full min-h-[160px] flex items-center justify-center">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-24 h-32 bg-blue-50 border border-blue-100 rounded-sm shadow-sm flex items-center justify-center">
                      <FileText size={32} className="text-blue-500" />
                    </div>
                    <div className="absolute -bottom-1 -right-1 transform translate-x-1/4 translate-y-1/4">
                      {getValidationStatusIcon()}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Format Details */}
            <div className="w-full md:w-2/3 p-6">
              {/* Metadata Section */}
              {(formatData || metadata) && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-gray-800">Examination Format</h4>
                    {getValidationStatusText()}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                    {metadata?.uploadedAt && (
                      <div className="flex items-center">
                        <Calendar className="text-gray-400 mr-2 h-4 w-4" />
                        <div>
                          <span className="text-gray-500">Uploaded:</span>{' '}
                          <span className="text-gray-700">{formatDate(metadata.uploadedAt)}</span>
                        </div>
                      </div>
                    )}
                    {metadata?.fileSize && (
                      <div className="flex items-center">
                        <FileText className="text-gray-400 mr-2 h-4 w-4" />
                        <div>
                          <span className="text-gray-500">Size:</span>{' '}
                          <span className="text-gray-700">{formatFileSize(metadata.fileSize)}</span>
                        </div>
                      </div>
                    )}
                    {metadata?.uploader && (
                      <div className="flex items-center">
                        <User className="text-gray-400 mr-2 h-4 w-4" />
                        <div>
                          <span className="text-gray-500">By:</span>{' '}
                          <span className="text-gray-700">{metadata.uploader}</span>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {/* Quick Actions */}
                  <div className="flex space-x-2 pt-3 mt-3 border-t border-gray-100">
                    {formatData?.url && (
                      <>
                        {isDataUrl && (
                          <a
                            href={formatData.url}
                            download="examination_format.pdf"
                            className="text-xs text-gray-600 hover:text-blue-600 flex items-center"
                          >
                            <Download className="mr-1 h-3 w-3" /> Download
                          </a>
                        )}
                        <button 
                          onClick={onReplaceClick}
                          className="text-xs text-gray-600 hover:text-blue-600 flex items-center"
                        >
                          <Replace className="mr-1 h-3 w-3" /> Replace
                        </button>
                      </>
                    )}
                  </div>
                </div>
              )}
              
              {/* Empty State */}
              {!formatData && !isLoading && (
                <div className="flex flex-col items-center justify-center h-full py-6">
                  <p className="text-gray-500 mb-2">No examination format available</p>
                  <button 
                    onClick={onReplaceClick}
                    className="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
                  >
                    <Upload className="mr-1 h-4 w-4" /> Upload Format
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Refresh Button */}
        <div className="mt-4 flex justify-end">
          <Button
            variant="outline"
            onClick={fetchFormat}
            disabled={isLoading}
            className="!w-auto text-xs"
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Format Dialog */}
      <ExaminationFormatDialog
        isOpen={isDialogOpen}
        onClose={closeDialog}
        format={formatData}
      />
    </>
  );
};